## Incoming Message Activity Diagram (salmate-frontend)

This activity diagram shows the complete flow in the frontend when a new message arrives, from backend WebSocket broadcast through window events, store updates, and UI reactions across ConversationHeader.svelte, ConversationView.svelte, MessageList.svelte, and PlatformIdentityList.svelte.

```mermaid
flowchart TD
  %% Sections: Backend -> Frontend WebSocket -> Event Bus -> List + Stores -> Conversation UI -> Errors

  %% Backend emits updates
  subgraph Backend
    BE1[Message persisted in DB]
    BE2[ASGI consumer emits event\n type = platform_message_update or new_message\n payload: platform_id, message, unread_count, etc]
    BE1 --> BE2
  end

  %% Frontend realtime layer
  subgraph Frontend Realtime - WebSocket
    WS1[PlatformWebSocket.ws.onmessage]
    WS2[Parse JSON]
    WS3{type?}
    WS4[Dispatch window event 'platform-new-message'\n detail: platformId, message, unreadCount; updateType?, batchId?]
    WS_ERR1[[Parse error -> log, ignore, keep connection]]
    WS_ERR2[[Socket error/close -> update connectionStatus,\n stop ping, schedule reconnect]]

    WS1 --> WS2 --> WS3
    WS3 -->|new_message or platform_message_update| WS4
    WS2 -->|error| WS_ERR1
  end

  BE2 --> WS1

  %% Window event bus
  subgraph UI Event Bus
    EV1[[window.dispatchEvent('platform-new-message')]]
  end
  WS4 --> EV1

  %% Conversation list & identity panel reacts first
  subgraph Conversation List - PlatformIdentityList.svelte
    PL1[handleNewMessage(detail)]
    PL2[latestMessages.set(platformId, message)]
    PL3[tabCacheStore.updateUnreadCount(platformId, unreadCount)]
    PL4[conversationStore.addMessage platformId,message\n dedupe handled in store]
    PL5[Re-sort identities\n move updated platform to top]

    EV1 --> PL1 --> PL2 --> PL5
    PL1 --> PL3
    PL1 --> PL4
  end

  %% Store updates propagate
  subgraph Store
    ST1[(conversationStore)]
    ST1 -->|messages for platformId updated| UI1
  end
  PL4 --> ST1

  %% Conversation view reacts to store changes
  subgraph Conversation UI
    UI1[ConversationView.svelte subscribes to conversationStore]
    UI2[MessageList.svelte reactive render]
    UI3{message.platform_id == active platformId?}
    UI4{User near bottom?}
    UI5[Auto-scroll to bottom\n show new message]
    UI6[Preserve scroll position\n show new message]
    UI7{Window focused AND not self AND status not READ?}
    UI8[Optionally mark-as-read via service/store\n (implementation-specific)]
    UI9[Keep unread; badge shows in list]
    UI10[ConversationHeader.svelte\n shows ticket status/owner;\n not directly changed by new messages]

    UI1 --> UI2 --> UI3
    UI3 -->|No| UI10
    UI3 -->|Yes| UI4
    UI4 -->|Yes| UI5 --> UI10
    UI4 -->|No| UI6 --> UI10
    UI2 --> UI7
    UI7 -->|Yes| UI8
    UI7 -->|No| UI9
  end

  %% Error and edge paths
  subgraph Errors
    ER1[[conversationStore.addMessage error -> log; keep UI responsive]]
    ER2[[Unread update error -> log; polling eventually corrects]]
    ER3[[Batch processing warning updateType/batchId -> log; optional toast]]
    ER4[[WebSocket close/error -> connectionStatus: disconnected;\n retry with backoff]]
  end
  PL4 -.-> ER1
  PL3 -.-> ER2
  WS4 -.-> ER3
  WS_ERR2 --> ER4

  %% Final states
  FS1[[Message displayed in MessageList for active platform]]
  FS2[[Left panel updated: latest snippet + unread badge]]
  FS3[[Optional: message marked READ when focused]]
  FS4[[Notifications: optional toast/log on errors]]

  UI5 --> FS1
  UI6 --> FS1
  PL2 --> FS2
  PL3 --> FS2
  UI8 --> FS3
  ER1 --> FS4
  ER2 --> FS4
  ER3 --> FS4
```

Notes and mapping to code:
- WebSocket handling: src/lib/websocket/platformWebSocket.ts (onmessage, handleNewMessage, handlePlatformMessageUpdate, window.dispatchEvent)
- Event listeners and UI reaction: src/lib/components/chat/PlatformIdentityList.svelte (window.addEventListener, handleNewMessage, update unread, conversationStore.addMessage)
- Store and data flow: src/lib/stores/conversationStore.ts (loadConversation, set/add messages)
- Conversation view reactivity: src/lib/components/conversation/ConversationView.svelte (subscribes to conversationStore); src/lib/components/conversation/MessageList.svelte (scroll logic on new messages)
- Header context: src/lib/components/conversation/ConversationHeader.svelte (ticket status/owner badges; not directly changed by new message)
- Backend event types: customer_consumer.py / ticket/chat_consumer.py emit platform_message_update/new_message types consumed by the frontend WebSocket wrapper.

